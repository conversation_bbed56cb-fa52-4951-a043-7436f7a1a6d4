const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// 暴露安全的API给渲染进程
contextBridge.exposeInMainWorld('electronAPI', {
  // 邮箱账号管理
  getEmailAccounts: () => ipcRenderer.invoke('get-email-accounts'),
  addEmailAccount: (accountData) => ipcRenderer.invoke('add-email-account', accountData),
  updateEmailAccount: (accountId, accountData) => ipcRenderer.invoke('update-email-account', accountId, accountData),
  deleteEmailAccount: (accountId) => ipcRenderer.invoke('delete-email-account', accountId),
  testEmailConnection: (accountData) => ipcRenderer.invoke('test-email-connection', accountData),

  // 验证码管理
  getVerificationCodes: (accountId, limit) => ipcRenderer.invoke('get-verification-codes', accountId, limit),

  // 连接管理
  startAllConnections: () => ipc<PERSON>enderer.invoke('start-all-connections'),
  stopAllConnections: () => ipcRenderer.invoke('stop-all-connections'),
  startEmailConnection: (accountId) => ipcRenderer.invoke('start-email-connection', accountId),
  stopEmailConnection: (accountId) => ipcRenderer.invoke('stop-email-connection', accountId),
  getConnectionStates: () => ipcRenderer.invoke('get-connection-states'),
  refreshEmails: (accountId) => ipcRenderer.invoke('refresh-emails', accountId),

  // 事件监听
  onNewVerificationCode: (callback) => {
    console.log('设置新验证码事件监听器');
    ipcRenderer.on('new-verification-code', (event, data) => {
      console.log('preload收到新验证码事件:', data);
      callback(data);
    });
  },

  onConnectionStatesUpdated: (callback) => {
    ipcRenderer.on('connection-states-updated', callback);
  },

  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // 系统信息
  platform: process.platform,
  
  // 工具函数
  copyToClipboard: async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      return true;
    } catch (error) {
      console.error('复制到剪贴板失败:', error);
      return false;
    }
  }
});

// 在窗口加载完成后初始化
window.addEventListener('DOMContentLoaded', () => {
  console.log('预加载脚本已加载');
});
