import { useState, useEffect, useCallback } from 'react';

export const useEmailAccounts = () => {
  const [accounts, setAccounts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [connectionStates, setConnectionStates] = useState({});

  // 获取所有邮箱账号
  const fetchAccounts = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }

      const accountsData = await window.electronAPI.getEmailAccounts();
      setAccounts(accountsData || []);

      // 同时获取连接状态
      await refreshConnectionStates();
    } catch (err) {
      console.error('获取邮箱账号失败:', err);
      setError(err.message || '获取邮箱账号失败');
    } finally {
      setLoading(false);
    }
  }, []);

  // 刷新连接状态
  const refreshConnectionStates = useCallback(async () => {
    try {
      if (!window.electronAPI) {
        return;
      }

      const states = await window.electronAPI.getConnectionStates();
      setConnectionStates(states || {});
    } catch (err) {
      console.error('获取连接状态失败:', err);
    }
  }, []);

  // 添加邮箱账号
  const addAccount = useCallback(async (accountData) => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }
      
      const newAccount = await window.electronAPI.addEmailAccount(accountData);
      if (newAccount) {
        setAccounts(prev => [...prev, newAccount]);
        return newAccount;
      }
    } catch (err) {
      console.error('添加邮箱账号失败:', err);
      throw new Error(err.message || '添加邮箱账号失败');
    }
  }, []);

  // 更新邮箱账号
  const updateAccount = useCallback(async (accountId, accountData) => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }
      
      const updatedAccount = await window.electronAPI.updateEmailAccount(accountId, accountData);
      if (updatedAccount) {
        setAccounts(prev => 
          prev.map(acc => acc.id === accountId ? updatedAccount : acc)
        );
        return updatedAccount;
      }
    } catch (err) {
      console.error('更新邮箱账号失败:', err);
      throw new Error(err.message || '更新邮箱账号失败');
    }
  }, []);

  // 删除邮箱账号
  const deleteAccount = useCallback(async (accountId) => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }
      
      const success = await window.electronAPI.deleteEmailAccount(accountId);
      if (success) {
        setAccounts(prev => prev.filter(acc => acc.id !== accountId));
        return true;
      }
    } catch (err) {
      console.error('删除邮箱账号失败:', err);
      throw new Error(err.message || '删除邮箱账号失败');
    }
  }, []);

  // 测试邮箱连接
  const testConnection = useCallback(async (accountData) => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }
      
      const result = await window.electronAPI.testEmailConnection(accountData);
      return result;
    } catch (err) {
      console.error('测试连接失败:', err);
      return {
        success: false,
        message: err.message || '测试连接失败',
        error: 'TEST_ERROR'
      };
    }
  }, []);

  // 启动单个连接
  const startConnection = useCallback(async (accountId) => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }

      const result = await window.electronAPI.startEmailConnection(accountId);

      // 刷新连接状态
      setTimeout(() => {
        refreshConnectionStates();
      }, 1000);

      return result;
    } catch (err) {
      console.error('启动连接失败:', err);
      // 即使失败也要刷新状态
      setTimeout(() => {
        refreshConnectionStates();
      }, 1000);
      throw new Error(err.message || '启动连接失败');
    }
  }, [refreshConnectionStates]);

  // 停止单个连接
  const stopConnection = useCallback(async (accountId) => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }

      const result = await window.electronAPI.stopEmailConnection(accountId);

      // 刷新连接状态
      setTimeout(() => {
        refreshConnectionStates();
      }, 500);

      return result;
    } catch (err) {
      console.error('停止连接失败:', err);
      // 即使失败也要刷新状态
      setTimeout(() => {
        refreshConnectionStates();
      }, 500);
      throw new Error(err.message || '停止连接失败');
    }
  }, [refreshConnectionStates]);

  // 启动所有连接
  const startAllConnections = useCallback(async () => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }

      const result = await window.electronAPI.startAllConnections();
      return result;
    } catch (err) {
      console.error('启动连接失败:', err);
      throw new Error(err.message || '启动连接失败');
    }
  }, []);

  // 停止所有连接
  const stopAllConnections = useCallback(async () => {
    try {
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }

      const result = await window.electronAPI.stopAllConnections();
      return result;
    } catch (err) {
      console.error('停止连接失败:', err);
      throw new Error(err.message || '停止连接失败');
    }
  }, []);

  // 刷新账号列表
  const refreshAccounts = useCallback(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  // 初始化时获取账号列表
  useEffect(() => {
    fetchAccounts();
  }, [fetchAccounts]);

  // 定期刷新连接状态，以反映后端的自动连接结果
  useEffect(() => {
    // 初始延迟，等待后端自动连接完成
    const initialTimer = setTimeout(() => {
      refreshConnectionStates();
    }, 4000);

    // 定期刷新连接状态
    const intervalTimer = setInterval(() => {
      refreshConnectionStates();
    }, 10000); // 每10秒刷新一次

    return () => {
      clearTimeout(initialTimer);
      clearInterval(intervalTimer);
    };
  }, [refreshConnectionStates]);

  // 监听后端连接状态更新事件
  useEffect(() => {
    const handleConnectionStatesUpdated = () => {
      console.log('收到连接状态更新通知，刷新状态...');
      refreshConnectionStates();
    };

    if (window.electronAPI) {
      window.electronAPI.onConnectionStatesUpdated(handleConnectionStatesUpdated);
    }

    return () => {
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners('connection-states-updated');
      }
    };
  }, [refreshConnectionStates]);

  return {
    accounts,
    loading,
    error,
    connectionStates,
    addAccount,
    updateAccount,
    deleteAccount,
    testConnection,
    startConnection,
    stopConnection,
    startAllConnections,
    stopAllConnections,
    refreshAccounts,
    refreshConnectionStates
  };
};
