import React, { useState, useEffect } from 'react';
import Sidebar from './components/Sidebar';
import AccountDashboard from './components/AccountDashboard';
import VerificationCodeView from './components/VerificationCodeView';
import AddAccountModal from './components/AddAccountModal';
import { useEmailAccounts } from './hooks/useEmailAccounts';
import { useVerificationCodes } from './hooks/useVerificationCodes';

function App() {
  const [currentView, setCurrentView] = useState('dashboard'); // 'dashboard' | 'verification'
  const [selectedAccountId, setSelectedAccountId] = useState(null);
  const [showAddModal, setShowAddModal] = useState(false);

  const {
    accounts,
    loading: accountsLoading,
    error: accountsError,
    connectionStates,
    addAccount,
    updateAccount,
    deleteAccount,
    testConnection,
    startConnection,
    stopConnection,
    startAllConnections,
    stopAllConnections,
    refreshAccounts,
    refreshConnectionStates
  } = useEmailAccounts();

  const {
    verificationCodes,
    loading: codesLoading,
    refreshCodes
  } = useVerificationCodes(selectedAccountId);

  // 选择第一个账号作为默认选中
  useEffect(() => {
    if (accounts.length > 0 && !selectedAccountId) {
      setSelectedAccountId(accounts[0].id);
    }
  }, [accounts, selectedAccountId]);

  // 监听新验证码
  useEffect(() => {
    const handleNewCode = (data) => {
      console.log('前端收到新验证码通知:', data);
      console.log('当前选中账号ID:', selectedAccountId);
      console.log('验证码账号ID:', data.accountId);

      // 刷新验证码列表（不管是否为当前选中账号）
      refreshCodes();

      // 显示系统通知
      if (window.Notification && Notification.permission === 'granted') {
        new Notification('收到新验证码', {
          body: `验证码: ${data.code.code}`,
          icon: '/icon.png'
        });
      }
    };

    if (window.electronAPI) {
      console.log('前端设置新验证码事件监听器');
      window.electronAPI.onNewVerificationCode(handleNewCode);
      console.log('新验证码事件监听器已设置');
    } else {
      console.error('window.electronAPI 不可用');
    }

    return () => {
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners('new-verification-code');
      }
    };
  }, [selectedAccountId, refreshCodes]);

  // 请求通知权限
  useEffect(() => {
    if (window.Notification && Notification.permission === 'default') {
      Notification.requestPermission();
    }
  }, []);

  const handleAccountSelect = (accountId) => {
    setSelectedAccountId(accountId);
  };

  const handleAddAccount = async (accountData) => {
    try {
      await addAccount(accountData);
      setShowAddModal(false);
    } catch (error) {
      console.error('添加账号失败:', error);
      throw error;
    }
  };

  const handleUpdateAccount = async (accountId, accountData) => {
    try {
      await updateAccount(accountId, accountData);
    } catch (error) {
      console.error('更新账号失败:', error);
      throw error;
    }
  };

  const handleDeleteAccount = async (accountId) => {
    try {
      await deleteAccount(accountId);
      // 如果删除的是当前选中的账号，清除选中状态
      if (accountId === selectedAccountId) {
        setSelectedAccountId(null);
      }
    } catch (error) {
      console.error('删除账号失败:', error);
      throw error;
    }
  };

  const selectedAccount = accounts.find(acc => acc.id === selectedAccountId);

  if (accountsError) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">加载失败</h2>
          <p className="text-gray-600 mb-4">{accountsError}</p>
          <button 
            onClick={refreshAccounts}
            className="btn-primary"
          >
            重试
          </button>
        </div>
      </div>
    );
  }

  const renderMainContent = () => {
    switch (currentView) {
      case 'dashboard':
        return (
          <AccountDashboard
            accounts={accounts}
            loading={accountsLoading}
            onUpdateAccount={handleUpdateAccount}
            onDeleteAccount={handleDeleteAccount}
            onViewCodes={(accountId) => {
              setSelectedAccountId(accountId);
              setCurrentView('verification');
            }}
            onStartConnection={startConnection}
            onStopConnection={stopConnection}
            connectionStates={connectionStates}
          />
        );
      case 'verification':
        return (
          <VerificationCodeView
            accounts={accounts}
            allVerificationCodes={verificationCodes}
            loading={codesLoading}
            onRefresh={refreshCodes}
            onUpdateAccount={handleUpdateAccount}
            onDeleteAccount={handleDeleteAccount}
            onBackToDashboard={() => setCurrentView('dashboard')}
            onStartConnection={startConnection}
            onStopConnection={stopConnection}
            connectionStates={connectionStates}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="flex h-screen overflow-hidden bg-slate-100">
      {/* 左侧边栏 */}
      <Sidebar
        currentView={currentView}
        onViewChange={setCurrentView}
        onAddAccount={() => setShowAddModal(true)}
      />

      {/* 主内容区域 */}
      {renderMainContent()}

      {/* 添加账号模态框 */}
      {showAddModal && (
        <AddAccountModal
          onClose={() => setShowAddModal(false)}
          onSubmit={handleAddAccount}
          onTestConnection={testConnection}
        />
      )}
    </div>
  );
}

export default App;
