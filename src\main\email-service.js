const Imap = require('imap');
const { simpleParser } = require('mailparser');
const Database = require('./database');

class EmailService {
  constructor(database = null) {
    this.connections = new Map(); // accountId -> connection info
    this.connectionStates = new Map(); // accountId -> 'connected' | 'disconnected' | 'failed' | 'connecting'
    this.verificationCodeRegex = /\b\d{4,8}\b/g; // 匹配4-8位数字验证码
    this.database = database;

    // 如果没有传入database实例，创建新的并初始化
    if (!this.database) {
      this.database = new Database();
      this.database.initialize().catch(console.error);
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionState(accountId) {
    return this.connectionStates.get(accountId) || 'disconnected';
  }

  /**
   * 获取所有连接状态
   */
  getAllConnectionStates() {
    const states = {};
    for (const [accountId, state] of this.connectionStates) {
      states[accountId] = state;
    }
    return states;
  }

  /**
   * 测试邮箱连接
   */
  async testConnection(accountData) {
    return new Promise((resolve) => {
      const imap = new Imap({
        user: accountData.email,
        password: accountData.password,
        host: accountData.imapHost || 'imap.mxhichina.com',
        port: accountData.imapPort || 993,
        tls: true,
        tlsOptions: { rejectUnauthorized: false },
        connTimeout: 10000,
        authTimeout: 5000
      });

      let resolved = false;

      const cleanup = () => {
        if (!resolved) {
          resolved = true;
          try {
            imap.end();
          } catch (error) {
            // 忽略清理错误
          }
        }
      };

      imap.once('ready', () => {
        if (!resolved) {
          resolved = true;
          cleanup();
          resolve({ success: true, message: '连接成功' });
        }
      });

      imap.once('error', (err) => {
        if (!resolved) {
          resolved = true;
          cleanup();
          resolve({ 
            success: false, 
            message: `连接失败: ${err.message}`,
            error: err.code || 'UNKNOWN_ERROR'
          });
        }
      });

      // 超时处理
      setTimeout(() => {
        if (!resolved) {
          resolved = true;
          cleanup();
          resolve({ 
            success: false, 
            message: '连接超时',
            error: 'TIMEOUT'
          });
        }
      }, 15000);

      try {
        imap.connect();
      } catch (error) {
        if (!resolved) {
          resolved = true;
          resolve({ 
            success: false, 
            message: `连接异常: ${error.message}`,
            error: 'CONNECTION_ERROR'
          });
        }
      }
    });
  }

  /**
   * 启动邮箱监听连接
   */
  async startConnection(account, onVerificationCode) {
    try {
      // 设置连接状态为连接中
      this.connectionStates.set(account.id, 'connecting');

      if (this.connections.has(account.id)) {
        await this.stopConnection(account.id);
      }

      // 验证账号信息
      if (!account.email || !account.password) {
        this.connectionStates.set(account.id, 'failed');
        throw new Error('邮箱地址或密码不能为空');
      }

      const imap = new Imap({
        user: account.email,
        password: account.password,
        host: account.imapHost || 'imap.mxhichina.com',
        port: account.imapPort || 993,
        tls: true,
        tlsOptions: { rejectUnauthorized: false },
        keepalive: true,
        connTimeout: 10000,
        authTimeout: 5000
      });

      const connectionInfo = {
        imap,
        account,
        onVerificationCode,
        isConnected: false,
        reconnectAttempts: 0,
        maxReconnectAttempts: 3, // 减少重连次数
        connectionStartTime: Date.now()
      };

      this.connections.set(account.id, connectionInfo);
      this.setupImapHandlers(connectionInfo);

      // 使用Promise包装连接过程，添加超时处理
      return new Promise((resolve, reject) => {
        let resolved = false;

        const cleanup = () => {
          if (!resolved) {
            resolved = true;
            this.connections.delete(account.id);
          }
        };

        // 连接超时处理
        const timeout = setTimeout(() => {
          if (!resolved) {
            resolved = true;
            cleanup();
            this.connectionStates.set(account.id, 'failed');
            reject(new Error('连接超时'));
          }
        }, 15000);

        // 连接成功
        imap.once('ready', () => {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeout);
            this.connectionStates.set(account.id, 'connected');
            console.log(`邮箱连接成功: ${account.email}`);
            resolve();
          }
        });

        // 连接失败
        imap.once('error', (err) => {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeout);
            cleanup();
            this.connectionStates.set(account.id, 'failed');
            console.error(`IMAP连接错误 [${account.email}]:`, err);
            reject(new Error(`连接失败: ${err.message}`));
          }
        });

        try {
          imap.connect();
        } catch (error) {
          if (!resolved) {
            resolved = true;
            clearTimeout(timeout);
            cleanup();
            this.connectionStates.set(account.id, 'failed');
            reject(new Error(`连接异常: ${error.message}`));
          }
        }
      });

    } catch (error) {
      console.error(`启动连接失败 [${account.email}]:`, error);
      this.connections.delete(account.id);
      this.connectionStates.set(account.id, 'failed');
      throw error;
    }
  }

  /**
   * 设置IMAP事件处理器
   */
  setupImapHandlers(connectionInfo) {
    const { imap, account } = connectionInfo;

    imap.once('ready', () => {
      try {
        console.log(`邮箱连接就绪: ${account.email}`);
        connectionInfo.isConnected = true;
        connectionInfo.reconnectAttempts = 0;
        connectionInfo.lastConnectedTime = Date.now();

        this.openInbox(connectionInfo);
      } catch (error) {
        console.error(`处理连接就绪事件失败 [${account.email}]:`, error);
      }
    });

    imap.on('error', (err) => {
      console.error(`IMAP连接错误 [${account.email}]:`, err);
      connectionInfo.isConnected = false;

      // 只有在连接存在且错误不是认证错误时才尝试重连
      if (this.connections.has(account.id) && !this.isAuthError(err)) {
        this.handleReconnect(connectionInfo);
      } else {
        console.log(`停止重连 [${account.email}]: 认证错误或连接已删除`);
        this.connections.delete(account.id);
      }
    });

    imap.once('end', () => {
      console.log(`IMAP连接结束: ${account.email}`);
      connectionInfo.isConnected = false;

      // 如果不是主动断开且连接时间超过30秒，尝试重连
      if (this.connections.has(account.id) &&
          connectionInfo.lastConnectedTime &&
          (Date.now() - connectionInfo.lastConnectedTime) > 30000) {
        this.handleReconnect(connectionInfo);
      }
    });
  }

  /**
   * 检查是否为认证错误
   */
  isAuthError(error) {
    const authErrorCodes = ['AUTHENTICATIONFAILED', 'LOGIN', 'AUTHENTICATE'];
    const errorMessage = error.message || error.toString();
    return authErrorCodes.some(code =>
      errorMessage.toUpperCase().includes(code)
    );
  }

  /**
   * 打开收件箱并监听新邮件
   */
  openInbox(connectionInfo) {
    const { imap, account, onVerificationCode } = connectionInfo;

    imap.openBox('INBOX', false, (err, box) => {
      if (err) {
        console.error(`打开收件箱失败 [${account.email}]:`, err);
        return;
      }

      console.log(`收件箱已打开: ${account.email}, 邮件总数: ${box.messages.total}`);

      // 监听新邮件
      imap.on('mail', (numNewMsgs) => {
        console.log(`收到 ${numNewMsgs} 封新邮件: ${account.email}`);
        this.fetchNewMessages(connectionInfo, numNewMsgs);
      });

      // 获取最近的几封邮件检查验证码
      this.fetchRecentMessages(connectionInfo);
    });
  }

  /**
   * 获取最近的邮件
   */
  fetchRecentMessages(connectionInfo, count = 5) {
    const { imap, account } = connectionInfo;

    // 获取最近的邮件，不管是否已读
    imap.search(['ALL'], (err, results) => {
      if (err) {
        console.error(`搜索邮件失败 [${account.email}]:`, err);
        return;
      }

      if (results.length === 0) {
        console.log(`没有邮件: ${account.email}`);
        return;
      }

      // 获取最近的邮件（取最后几封）
      const recentResults = results.slice(-count);
      console.log(`开始处理最近 ${recentResults.length} 封邮件: ${account.email}`);
      this.processMessages(connectionInfo, recentResults);
    });
  }

  /**
   * 获取新邮件
   */
  fetchNewMessages(connectionInfo, numNewMsgs) {
    const { imap } = connectionInfo;

    // 获取最新的邮件
    imap.search(['UNSEEN'], (err, results) => {
      if (err || !results.length) return;

      const newResults = results.slice(-numNewMsgs);
      this.processMessages(connectionInfo, newResults);
    });
  }

  /**
   * 处理邮件消息
   */
  processMessages(connectionInfo, messageIds) {
    const { imap, account, onVerificationCode } = connectionInfo;

    if (!messageIds.length) return;

    const fetch = imap.fetch(messageIds, {
      bodies: '',
      markSeen: false
    });

    fetch.on('message', (msg, seqno) => {
      let emailData = '';

      msg.on('body', (stream) => {
        stream.on('data', (chunk) => {
          emailData += chunk.toString('utf8');
        });

        stream.once('end', () => {
          this.parseEmailForVerificationCode(emailData, account, onVerificationCode);
        });
      });

      msg.once('error', (err) => {
        console.error(`处理邮件错误 [${account.email}]:`, err);
      });
    });

    fetch.once('error', (err) => {
      console.error(`获取邮件错误 [${account.email}]:`, err);
    });

    fetch.once('end', () => {
      console.log(`邮件处理完成: ${account.email}`);
    });
  }

  /**
   * 解析邮件内容查找验证码
   */
  async parseEmailForVerificationCode(emailData, account, onVerificationCode) {
    try {
      const parsed = await simpleParser(emailData);
      const subject = parsed.subject || '';
      const textContent = parsed.text || '';
      const htmlContent = parsed.html || '';

      console.log(`解析邮件 [${account.email}] - 主题: ${subject}`);
      console.log(`邮件发件人: ${parsed.from?.text || '未知发件人'}`);

      // 在主题和内容中查找验证码
      const allContent = `${subject} ${textContent} ${htmlContent}`;
      const codes = allContent.match(this.verificationCodeRegex);

      console.log(`验证码搜索结果 [${account.email}]:`, codes);

      if (codes && codes.length > 0) {
        // 使用邮件的实际接收时间，如果没有则使用当前时间
        const emailDate = parsed.date || new Date();

        // 取第一个匹配的验证码
        const verificationCode = {
          code: codes[0],
          subject: subject,
          from: parsed.from?.text || '未知发件人',
          receivedAt: emailDate,
          accountId: account.id
        };

        console.log(`发现验证码 [${account.email}]:`, verificationCode.code);
        console.log(`邮件接收时间: ${emailDate.toLocaleString('zh-CN')}`);

        // 保存到数据库
        await this.saveVerificationCode(verificationCode);

        // 通知渲染进程
        console.log('准备通知前端新验证码:', verificationCode);
        if (typeof onVerificationCode === 'function') {
          onVerificationCode(verificationCode);
          console.log('已调用onVerificationCode回调');
        } else {
          console.error('onVerificationCode不是一个函数:', typeof onVerificationCode);
        }
      } else {
        console.log(`未在邮件中找到验证码 [${account.email}] - 主题: ${subject}`);
      }
    } catch (error) {
      console.error(`解析邮件失败 [${account.email}]:`, error);
    }
  }

  /**
   * 保存验证码到数据库
   */
  async saveVerificationCode(verificationCode) {
    try {
      await this.database.saveVerificationCode(verificationCode);
      console.log('验证码已保存到数据库:', verificationCode.code);
    } catch (error) {
      console.error('保存验证码失败:', error);
    }
  }

  /**
   * 处理重连
   */
  handleReconnect(connectionInfo) {
    const { account } = connectionInfo;

    // 检查是否应该停止重连
    if (!this.connections.has(account.id)) {
      console.log(`连接已被删除，停止重连: ${account.email}`);
      return;
    }

    if (connectionInfo.reconnectAttempts >= connectionInfo.maxReconnectAttempts) {
      console.error(`重连次数超限，停止重连: ${account.email}`);
      this.connections.delete(account.id);
      return;
    }

    connectionInfo.reconnectAttempts++;
    const delay = Math.min(5000 * connectionInfo.reconnectAttempts, 60000); // 增加延迟时间

    console.log(`${delay/1000}秒后尝试重连 [${account.email}] (第${connectionInfo.reconnectAttempts}次)`);

    const reconnectTimeout = setTimeout(() => {
      if (this.connections.has(account.id) && !connectionInfo.isConnected) {
        console.log(`开始重连: ${account.email}`);
        try {
          // 创建新的IMAP实例进行重连
          const newImap = new Imap({
            user: account.email,
            password: account.password,
            host: account.imapHost || 'imap.mxhichina.com',
            port: account.imapPort || 993,
            tls: true,
            tlsOptions: { rejectUnauthorized: false },
            keepalive: true,
            connTimeout: 10000,
            authTimeout: 5000
          });

          // 更新连接信息
          connectionInfo.imap = newImap;
          this.setupImapHandlers(connectionInfo);

          newImap.connect();
        } catch (error) {
          console.error(`重连失败 [${account.email}]:`, error);
          // 继续尝试重连
          setTimeout(() => this.handleReconnect(connectionInfo), 5000);
        }
      }
    }, delay);

    // 保存超时ID以便可能的清理
    connectionInfo.reconnectTimeout = reconnectTimeout;
  }

  /**
   * 停止指定账号的连接
   */
  async stopConnection(accountId) {
    const connectionInfo = this.connections.get(accountId);
    if (!connectionInfo) return;

    try {
      // 清理重连超时
      if (connectionInfo.reconnectTimeout) {
        clearTimeout(connectionInfo.reconnectTimeout);
        connectionInfo.reconnectTimeout = null;
      }

      // 标记为主动断开
      connectionInfo.isConnected = false;

      // 安全地关闭IMAP连接
      if (connectionInfo.imap) {
        connectionInfo.imap.removeAllListeners();
        connectionInfo.imap.end();
      }
    } catch (error) {
      console.error(`关闭连接失败 [${accountId}]:`, error);
      // 即使关闭失败，也要删除连接记录
    }

    this.connections.delete(accountId);
    this.connectionStates.set(accountId, 'disconnected');
    console.log(`连接已停止: ${accountId}`);
  }

  /**
   * 停止所有连接
   */
  async stopAllConnections() {
    const promises = Array.from(this.connections.keys()).map(accountId => 
      this.stopConnection(accountId)
    );
    
    await Promise.all(promises);
    console.log('所有邮箱连接已停止');
  }

  /**
   * 获取连接状态
   */
  getConnectionStatus(accountId) {
    const connectionInfo = this.connections.get(accountId);
    return connectionInfo ? {
      isConnected: connectionInfo.isConnected,
      reconnectAttempts: connectionInfo.reconnectAttempts
    } : null;
  }

  /**
   * 手动刷新单个账号的邮件
   */
  async refreshEmails(accountId) {
    const connectionInfo = this.connections.get(accountId);
    if (!connectionInfo || !connectionInfo.isConnected) {
      throw new Error('邮箱未连接');
    }

    console.log(`手动刷新邮件: ${connectionInfo.account.email}`);
    this.fetchRecentMessages(connectionInfo, 10); // 获取最近10封邮件
  }

  /**
   * 手动刷新所有账号的邮件
   */
  async refreshAllEmails() {
    const refreshPromises = [];

    for (const [accountId, connectionInfo] of this.connections) {
      if (connectionInfo.isConnected) {
        console.log(`刷新邮件: ${connectionInfo.account.email}`);
        refreshPromises.push(
          new Promise((resolve) => {
            try {
              this.fetchRecentMessages(connectionInfo, 10);
              resolve();
            } catch (error) {
              console.error(`刷新邮件失败 [${connectionInfo.account.email}]:`, error);
              resolve(); // 不阻塞其他账号的刷新
            }
          })
        );
      }
    }

    await Promise.all(refreshPromises);
    console.log('所有邮件刷新完成');
  }
}

module.exports = EmailService;
