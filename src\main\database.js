const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const { app } = require('electron');
const Store = require('electron-store');

class Database {
  constructor() {
    this.db = null;
    this.store = new Store({
      name: 'email-accounts',
      encryptionKey: 'your-encryption-key-here' // 生产环境应使用更安全的密钥
    });
  }

  /**
   * 初始化数据库
   */
  async initialize() {
    try {
      const userDataPath = app.getPath('userData');
      const dbPath = path.join(userDataPath, 'email-verification.db');

      console.log('初始化数据库:', dbPath);

      return new Promise((resolve, reject) => {
        this.db = new sqlite3.Database(dbPath, sqlite3.OPEN_READWRITE | sqlite3.OPEN_CREATE, (err) => {
          if (err) {
            console.error('数据库连接失败:', err);
            // 尝试创建用户数据目录
            try {
              const fs = require('fs');
              if (!fs.existsSync(userDataPath)) {
                fs.mkdirSync(userDataPath, { recursive: true });
              }
              // 重试连接
              this.db = new sqlite3.Database(dbPath, (retryErr) => {
                if (retryErr) {
                  console.error('数据库重试连接失败:', retryErr);
                  reject(retryErr);
                } else {
                  console.log('数据库重试连接成功:', dbPath);
                  this.createTables().then(resolve).catch(reject);
                }
              });
            } catch (fsError) {
              console.error('创建用户数据目录失败:', fsError);
              reject(err);
            }
            return;
          }

          console.log('数据库连接成功:', dbPath);
          this.createTables().then(resolve).catch(reject);
        });
      });
    } catch (error) {
      console.error('数据库初始化异常:', error);
      throw error;
    }
  }

  /**
   * 创建数据表
   */
  async createTables() {
    return new Promise((resolve, reject) => {
      // 创建验证码表
      const createVerificationCodesTable = `
        CREATE TABLE IF NOT EXISTS verification_codes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          account_id TEXT NOT NULL,
          code TEXT NOT NULL,
          subject TEXT,
          sender TEXT,
          received_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `;

      // 创建索引
      const createIndexes = [
        'CREATE INDEX IF NOT EXISTS idx_account_id ON verification_codes(account_id)',
        'CREATE INDEX IF NOT EXISTS idx_received_at ON verification_codes(received_at)',
        'CREATE INDEX IF NOT EXISTS idx_code ON verification_codes(code)'
      ];

      this.db.run(createVerificationCodesTable, (err) => {
        if (err) {
          console.error('创建验证码表失败:', err);
          reject(err);
          return;
        }

        // 创建索引
        const promises = createIndexes.map(indexSql => 
          new Promise((resolveIndex, rejectIndex) => {
            this.db.run(indexSql, (err) => {
              if (err) {
                console.error('创建索引失败:', err);
                rejectIndex(err);
              } else {
                resolveIndex();
              }
            });
          })
        );

        Promise.all(promises)
          .then(() => {
            console.log('Database tables and indexes created successfully');
            resolve();
          })
          .catch(reject);
      });
    });
  }

  /**
   * 获取所有邮箱账号
   */
  async getEmailAccounts() {
    try {
      const accounts = this.store.get('accounts', []);
      return accounts.map(account => ({
        ...account,
        // 解密密码
        password: this.decryptPassword(account.encryptedPassword)
      }));
    } catch (error) {
      console.error('获取邮箱账号失败:', error);
      return [];
    }
  }

  /**
   * 根据ID获取单个邮箱账号
   */
  async getEmailAccount(accountId) {
    try {
      const accounts = this.store.get('accounts', []);
      const account = accounts.find(acc => acc.id === accountId);

      if (!account) {
        return null;
      }

      return {
        ...account,
        // 解密密码
        password: this.decryptPassword(account.encryptedPassword)
      };
    } catch (error) {
      console.error('获取邮箱账号失败:', error);
      return null;
    }
  }

  /**
   * 添加邮箱账号
   */
  async addEmailAccount(accountData) {
    try {
      const accounts = this.store.get('accounts', []);
      
      // 检查邮箱是否已存在
      const existingAccount = accounts.find(acc => acc.email === accountData.email);
      if (existingAccount) {
        throw new Error('该邮箱账号已存在');
      }

      const newAccount = {
        id: Date.now().toString(),
        email: accountData.email,
        displayName: accountData.displayName || accountData.email,
        imapHost: accountData.imapHost || 'imap.mxhichina.com',
        imapPort: accountData.imapPort || 993,
        encryptedPassword: this.encryptPassword(accountData.password),
        enabled: accountData.enabled !== false,
        createdAt: new Date().toISOString()
      };

      accounts.push(newAccount);
      this.store.set('accounts', accounts);

      console.log('邮箱账号添加成功:', newAccount.email);
      
      // 返回解密后的账号信息
      return {
        ...newAccount,
        password: accountData.password
      };
    } catch (error) {
      console.error('添加邮箱账号失败:', error);
      throw error;
    }
  }

  /**
   * 更新邮箱账号
   */
  async updateEmailAccount(accountId, accountData) {
    try {
      const accounts = this.store.get('accounts', []);
      const accountIndex = accounts.findIndex(acc => acc.id === accountId);
      
      if (accountIndex === -1) {
        throw new Error('账号不存在');
      }

      const updatedAccount = {
        ...accounts[accountIndex],
        displayName: accountData.displayName || accounts[accountIndex].displayName,
        imapHost: accountData.imapHost || accounts[accountIndex].imapHost,
        imapPort: accountData.imapPort || accounts[accountIndex].imapPort,
        enabled: accountData.enabled !== undefined ? accountData.enabled : accounts[accountIndex].enabled,
        updatedAt: new Date().toISOString()
      };

      // 如果提供了新密码，则更新密码
      if (accountData.password) {
        updatedAccount.encryptedPassword = this.encryptPassword(accountData.password);
      }

      accounts[accountIndex] = updatedAccount;
      this.store.set('accounts', accounts);

      console.log('邮箱账号更新成功:', updatedAccount.email);
      
      // 返回解密后的账号信息
      return {
        ...updatedAccount,
        password: accountData.password || this.decryptPassword(updatedAccount.encryptedPassword)
      };
    } catch (error) {
      console.error('更新邮箱账号失败:', error);
      throw error;
    }
  }

  /**
   * 删除邮箱账号
   */
  async deleteEmailAccount(accountId) {
    try {
      const accounts = this.store.get('accounts', []);
      const filteredAccounts = accounts.filter(acc => acc.id !== accountId);
      
      if (filteredAccounts.length === accounts.length) {
        throw new Error('账号不存在');
      }

      this.store.set('accounts', filteredAccounts);

      // 同时删除相关的验证码记录
      await this.deleteVerificationCodesByAccount(accountId);

      console.log('邮箱账号删除成功:', accountId);
      return true;
    } catch (error) {
      console.error('删除邮箱账号失败:', error);
      throw error;
    }
  }

  /**
   * 保存验证码
   */
  async saveVerificationCode(verificationCode) {
    return new Promise((resolve, reject) => {
      const sql = `
        INSERT INTO verification_codes (account_id, code, subject, sender, received_at)
        VALUES (?, ?, ?, ?, ?)
      `;
      
      const params = [
        verificationCode.accountId,
        verificationCode.code,
        verificationCode.subject,
        verificationCode.from,
        verificationCode.receivedAt.toISOString()
      ];

      this.db.run(sql, params, function(err) {
        if (err) {
          console.error('保存验证码失败:', err);
          reject(err);
        } else {
          console.log('验证码保存成功, ID:', this.lastID);
          console.log('保存的验证码数据:', {
            accountId: params[0],
            code: params[1],
            subject: params[2],
            sender: params[3],
            receivedAt: params[4]
          });
          resolve(this.lastID);
        }
      });
    });
  }

  /**
   * 获取验证码历史
   */
  async getVerificationCodes(accountId, limit = 50) {
    return new Promise((resolve, reject) => {
      let sql = `
        SELECT * FROM verification_codes 
        WHERE account_id = ?
        ORDER BY received_at DESC
        LIMIT ?
      `;
      
      this.db.all(sql, [accountId, limit], (err, rows) => {
        if (err) {
          console.error('获取验证码历史失败:', err);
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  /**
   * 删除账号相关的验证码
   */
  async deleteVerificationCodesByAccount(accountId) {
    return new Promise((resolve, reject) => {
      const sql = 'DELETE FROM verification_codes WHERE account_id = ?';
      
      this.db.run(sql, [accountId], function(err) {
        if (err) {
          console.error('删除验证码记录失败:', err);
          reject(err);
        } else {
          console.log('删除验证码记录成功, 影响行数:', this.changes);
          resolve(this.changes);
        }
      });
    });
  }

  /**
   * 加密密码
   */
  encryptPassword(password) {
    // 简单的加密实现，生产环境应使用更强的加密算法
    return Buffer.from(password).toString('base64');
  }

  /**
   * 解密密码
   */
  decryptPassword(encryptedPassword) {
    try {
      return Buffer.from(encryptedPassword, 'base64').toString();
    } catch (error) {
      console.error('密码解密失败:', error);
      return '';
    }
  }

  /**
   * 关闭数据库连接
   */
  close() {
    if (this.db) {
      this.db.close((err) => {
        if (err) {
          console.error('关闭数据库失败:', err);
        } else {
          console.log('数据库连接已关闭');
        }
      });
    }
  }
}

module.exports = Database;
