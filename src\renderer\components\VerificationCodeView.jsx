import React, { useState } from 'react';
import { RefreshCw, Mail, ArrowLeft, Copy, Play, Square, Wifi, WifiOff } from 'lucide-react';

const VerificationCodeView = ({
  accounts,
  allVerificationCodes,
  loading,
  onRefresh,
  onUpdateAccount,
  onDeleteAccount,
  onBackToDashboard,
  onStartConnection,
  onStopConnection,
  connectionStates
}) => {
  const [connectingAccounts, setConnectingAccounts] = useState(new Set());
  const [refreshing, setRefreshing] = useState(false);
  const handleCopyCode = async (code) => {
    try {
      await navigator.clipboard.writeText(code);
      console.log('验证码已复制到剪贴板');
    } catch (error) {
      console.error('复制失败:', error);
    }
  };

  // 刷新所有邮件
  const handleRefreshEmails = async () => {
    try {
      setRefreshing(true);

      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }

      // 调用后端刷新邮件
      const result = await window.electronAPI.refreshEmails();

      if (result && result.success) {
        console.log('邮件刷新成功');
        // 延迟刷新验证码列表，给邮件处理一些时间
        setTimeout(() => {
          onRefresh();
        }, 2000);
      } else {
        console.error('邮件刷新失败:', result?.message);
      }
    } catch (error) {
      console.error('刷新邮件失败:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleStartConnection = async (accountId) => {
    setConnectingAccounts(prev => new Set([...prev, accountId]));
    try {
      const result = await onStartConnection(accountId);
      if (!result || !result.success) {
        console.error('启动连接失败:', result?.error);
      }
    } catch (error) {
      console.error('启动连接失败:', error);
    } finally {
      setConnectingAccounts(prev => {
        const newSet = new Set(prev);
        newSet.delete(accountId);
        return newSet;
      });
    }
  };

  const handleStopConnection = async (accountId) => {
    setConnectingAccounts(prev => new Set([...prev, accountId]));
    try {
      const result = await onStopConnection(accountId);
      if (!result || !result.success) {
        console.error('停止连接失败:', result?.error);
      }
    } catch (error) {
      console.error('停止连接失败:', error);
    } finally {
      setConnectingAccounts(prev => {
        const newSet = new Set(prev);
        newSet.delete(accountId);
        return newSet;
      });
    }
  };

  const getConnectionStatus = (account) => {
    const isConnecting = connectingAccounts.has(account.id);
    const connectionState = connectionStates[account.id];

    if (isConnecting) {
      return { status: 'connecting', label: '连接中...', color: 'yellow' };
    }

    if (connectionState === 'connected') {
      return { status: 'connected', label: '已连接', color: 'green' };
    }

    if (connectionState === 'failed') {
      return { status: 'failed', label: '连接失败', color: 'red' };
    }

    return { status: 'disconnected', label: '未连接', color: 'gray' };
  };

  // 获取每个账号的最新验证码
  const getLatestCodeForAccount = (accountId) => {
    const accountCodes = allVerificationCodes.filter(code => code.account_id === accountId);
    return accountCodes.sort((a, b) => new Date(b.received_at) - new Date(a.received_at))[0];
  };

  // 格式化时间显示
  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInMinutes = Math.floor((now - date) / (1000 * 60));

    // 显示相对时间和完整日期时间
    let relativeTime;
    if (diffInMinutes < 1) {
      relativeTime = '刚刚';
    } else if (diffInMinutes < 60) {
      relativeTime = `${diffInMinutes}分钟前`;
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60);
      relativeTime = `${hours}小时前`;
    } else {
      const days = Math.floor(diffInMinutes / 1440);
      relativeTime = `${days}天前`;
    }

    // 完整的日期时间
    const fullDateTime = date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });

    return `${relativeTime} (${fullDateTime})`;
  };

  return (
    <main className="flex-1 flex flex-col overflow-hidden bg-slate-100">
      <div className="p-6 lg:p-8 w-full h-full flex flex-col">
        {/* 页面标题 */}
        <div className="mb-6">
          <button
            onClick={onBackToDashboard}
            className="flex items-center gap-2 text-slate-600 hover:text-slate-900 hover:bg-white px-3 py-2 rounded-lg transition-all duration-200 mb-4"
          >
            <ArrowLeft className="w-4 h-4" />
            <span className="text-sm font-medium">返回账号看板</span>
          </button>

          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-slate-900 mb-1">验证码概览</h1>
              <p className="text-sm text-slate-600">查看所有邮箱账号的最新验证码</p>
            </div>
            <button
              onClick={handleRefreshEmails}
              disabled={loading || refreshing}
              className="flex items-center gap-2 px-4 py-2.5 bg-white text-slate-700 font-medium text-sm border border-slate-200 rounded-lg shadow-sm hover:bg-slate-50 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 transition-all duration-200"
            >
              <RefreshCw className={`w-4 h-4 ${(loading || refreshing) ? 'animate-spin' : ''}`} />
              <span>{refreshing ? '刷新中...' : '刷新全部'}</span>
            </button>
          </div>
        </div>

        {/* 账号列表 */}
        <div className="flex-1 overflow-y-auto">
          {loading ? (
            <div className="bg-white p-12 rounded-xl shadow-sm border border-slate-200 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-slate-600 font-medium">加载验证码数据中...</p>
            </div>
          ) : accounts.length === 0 ? (
            <div className="bg-white p-12 rounded-xl shadow-sm border border-slate-200 text-center">
              <Mail size={48} className="mx-auto mb-4 text-slate-300" />
              <h3 className="text-lg font-semibold text-slate-900 mb-2">暂无邮箱账号</h3>
              <p className="text-slate-600">请先添加邮箱账号以接收验证码</p>
            </div>
          ) : (
            <div className="space-y-4 pb-4">
              {accounts.map((account) => {
                const latestCode = getLatestCodeForAccount(account.id);
                const connectionStatus = getConnectionStatus(account);
                const isConnecting = connectingAccounts.has(account.id);

                return (
                  <div key={account.id} className="bg-white p-6 rounded-xl shadow-sm border border-slate-200 hover:shadow-md hover:border-slate-300 transition-all duration-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        {/* 账号信息 */}
                        <div className="flex items-center gap-3 mb-4">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Mail className="w-5 h-5 text-blue-600" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <h3 className="text-lg font-semibold text-slate-900 truncate">{account.email}</h3>
                            <div className="flex items-center gap-2">
                              <span className={`inline-flex items-center gap-1.5 px-2 py-1 text-xs font-medium rounded-full ${
                                connectionStatus.color === 'green' ? 'text-green-700 bg-green-100' :
                                connectionStatus.color === 'yellow' ? 'text-yellow-700 bg-yellow-100' :
                                'text-red-700 bg-red-100'
                              }`}>
                                <span className={`w-1.5 h-1.5 rounded-full ${
                                  connectionStatus.color === 'green' ? 'bg-green-500' :
                                  connectionStatus.color === 'yellow' ? 'bg-yellow-500 animate-pulse' :
                                  'bg-red-500'
                                }`}></span>
                                {connectionStatus.label}
                              </span>
                            </div>
                          </div>

                          {/* 连接控制按钮 */}
                          <div className="flex items-center gap-2">
                            {connectionStatus.status === 'connected' ? (
                              <button
                                onClick={() => handleStopConnection(account.id)}
                                disabled={isConnecting}
                                className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-red-700 bg-red-50 hover:bg-red-100 rounded-lg transition-colors disabled:opacity-50"
                                title="停止连接"
                              >
                                <Square className="w-3 h-3" />
                                停止
                              </button>
                            ) : (
                              <button
                                onClick={() => handleStartConnection(account.id)}
                                disabled={isConnecting}
                                className="flex items-center gap-1 px-3 py-1.5 text-xs font-medium text-green-700 bg-green-50 hover:bg-green-100 rounded-lg transition-colors disabled:opacity-50"
                                title="启动连接"
                              >
                                <Play className="w-3 h-3" />
                                {isConnecting ? '连接中...' : '启动'}
                              </button>
                            )}
                          </div>
                        </div>

                        {/* 最新验证码 */}
                        {latestCode ? (
                          <div className="bg-slate-50 p-4 rounded-lg">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-slate-600">最新验证码</span>
                              <span className="text-xs text-slate-500">{formatTime(latestCode.received_at)}</span>
                            </div>
                            <div className="flex items-center justify-between">
                              <div className="text-2xl font-bold text-blue-600 font-mono tracking-wider">
                                {latestCode.code}
                              </div>
                              <button
                                onClick={() => handleCopyCode(latestCode.code)}
                                className="flex items-center justify-center w-10 h-10 bg-white rounded-lg text-slate-600 hover:bg-blue-100 hover:text-blue-700 transition-all duration-200"
                                title="复制验证码"
                              >
                                <Copy className="w-4 h-4" />
                              </button>
                            </div>
                            <p className="text-xs text-slate-500 mt-2 truncate">
                              {latestCode.subject || `来自: ${latestCode.sender || '未知发件人'}`}
                            </p>
                          </div>
                        ) : (
                          <div className="bg-slate-50 p-4 rounded-lg text-center">
                            <p className="text-sm text-slate-500">暂无验证码</p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    </main>
  );
};

export default VerificationCodeView;
