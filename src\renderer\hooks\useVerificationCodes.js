import { useState, useEffect, useCallback } from 'react';

export const useVerificationCodes = (accountId) => {
  const [verificationCodes, setVerificationCodes] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // 获取验证码历史
  const fetchCodes = useCallback(async (limit = 50) => {
    if (!accountId) {
      setVerificationCodes([]);
      return;
    }

    try {
      setLoading(true);
      setError(null);
      
      if (!window.electronAPI) {
        throw new Error('Electron API 不可用');
      }
      
      const codes = await window.electronAPI.getVerificationCodes(accountId, limit);
      setVerificationCodes(codes || []);
    } catch (err) {
      console.error('获取验证码失败:', err);
      setError(err.message || '获取验证码失败');
      setVerificationCodes([]);
    } finally {
      setLoading(false);
    }
  }, [accountId]);

  // 刷新验证码列表
  const refreshCodes = useCallback((limit = 50) => {
    fetchCodes(limit);
  }, [fetchCodes]);

  // 添加新验证码到列表顶部
  const addNewCode = useCallback((newCode) => {
    setVerificationCodes(prev => {
      // 检查是否已存在相同的验证码（避免重复）
      const exists = prev.some(code => 
        code.code === newCode.code && 
        Math.abs(new Date(code.received_at) - new Date(newCode.receivedAt)) < 1000
      );
      
      if (exists) {
        return prev;
      }
      
      // 将新验证码添加到列表顶部
      const updatedCodes = [
        {
          id: Date.now(), // 临时ID，实际应该从数据库获取
          account_id: newCode.accountId,
          code: newCode.code,
          subject: newCode.subject,
          sender: newCode.from,
          received_at: newCode.receivedAt.toISOString(),
          created_at: newCode.receivedAt.toISOString()
        },
        ...prev
      ];
      
      // 限制列表长度，避免内存占用过多
      return updatedCodes.slice(0, 100);
    });
  }, []);

  // 当账号ID变化时重新获取验证码
  useEffect(() => {
    fetchCodes();
  }, [fetchCodes]);

  // 定期刷新验证码数据（每10秒）
  useEffect(() => {
    if (!accountId) return;

    const interval = setInterval(() => {
      console.log('定期刷新验证码数据...');
      fetchCodes();
    }, 10000);

    return () => clearInterval(interval);
  }, [accountId, fetchCodes]);

  // 监听新验证码事件
  useEffect(() => {
    if (!accountId) return;

    const handleNewCode = (data) => {
      // 只处理当前选中账号的验证码
      if (data.accountId === accountId) {
        addNewCode(data.code);
      }
    };

    // 这里不直接监听，因为在App组件中已经处理了
    // 如果需要在这里监听，可以取消App组件中的监听
    
    return () => {
      // 清理函数
    };
  }, [accountId, addNewCode]);

  return {
    verificationCodes,
    loading,
    error,
    refreshCodes,
    addNewCode
  };
};
