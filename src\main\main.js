const { app, <PERSON><PERSON>erWindow, ipc<PERSON><PERSON>, <PERSON>u } = require('electron');
const path = require('path');
const isDev = process.env.NODE_ENV === 'development';
const EmailService = require('./email-service');
const Database = require('./database');

class MainProcess {
  constructor() {
    this.mainWindow = null;
    this.database = new Database();
    this.emailService = new EmailService(this.database);
  }

  async initialize() {
    try {
      await this.database.initialize();
      this.setupApp();
      this.setupIPC();
      console.log('主进程初始化成功');
    } catch (error) {
      console.error('主进程初始化失败:', error);
      // 不要因为数据库初始化失败而阻止应用启动
      this.setupApp();
      this.setupIPC();
    }
  }

  /**
   * 自动连接所有已保存的邮箱账号
   */
  async startAutoConnections() {
    try {
      console.log('开始自动连接邮箱账号...');
      const accounts = await this.database.getEmailAccounts();

      if (accounts.length === 0) {
        console.log('没有找到已保存的邮箱账号');
        return;
      }

      console.log(`找到 ${accounts.length} 个邮箱账号，开始自动连接...`);

      // 并行启动所有账号的连接，但不等待结果
      const connectionPromises = accounts
        .filter(account => account.enabled !== false)
        .map(async (account) => {
          try {
            console.log(`尝试连接邮箱: ${account.email}`);
            await this.emailService.startConnection(account, (verificationCode) => {
              console.log('主进程收到验证码回调:', verificationCode);
              if (this.mainWindow && !this.mainWindow.isDestroyed()) {
                console.log('发送验证码通知到前端...');
                this.mainWindow.webContents.send('new-verification-code', {
                  accountId: account.id,
                  code: verificationCode
                });
                console.log('验证码通知已发送到前端');
              } else {
                console.error('主窗口不可用，无法发送验证码通知');
              }
            });
            console.log(`邮箱连接成功: ${account.email}`);
          } catch (error) {
            console.log(`邮箱连接失败: ${account.email} - ${error.message}`);
            // 连接失败不抛出错误，只记录日志
          }
        });

      // 不等待所有连接完成，让它们在后台进行
      Promise.allSettled(connectionPromises).then((results) => {
        const successful = results.filter(r => r.status === 'fulfilled').length;
        const failed = results.filter(r => r.status === 'rejected').length;
        console.log(`自动连接完成: ${successful} 个成功, ${failed} 个失败`);

        // 通知前端刷新连接状态
        if (this.mainWindow && !this.mainWindow.isDestroyed()) {
          this.mainWindow.webContents.send('connection-states-updated');
        }
      });

    } catch (error) {
      console.error('自动连接过程中发生错误:', error);
    }
  }

  setupApp() {
    // 当所有窗口关闭时退出应用 (macOS除外)
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        app.quit();
      }
    });

    // macOS激活应用时重新创建窗口
    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow();
      }
    });

    // 应用准备就绪时创建窗口
    app.whenReady().then(() => {
      this.createWindow();
      this.setupMenu();
    });
  }

  createWindow() {
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, '../preload/preload.js')
      },
      titleBarStyle: 'default',
      show: false
    });

    // 窗口准备好后显示
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();

      // 窗口显示后延迟启动自动连接，确保前端已完全加载
      setTimeout(() => {
        this.startAutoConnections();
      }, 3000);
    });

    // 加载应用
    if (isDev) {
      this.mainWindow.loadURL('http://localhost:3000');
      // 开发环境不自动打开开发者工具
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../../build/index.html'));
    }

    // 窗口关闭时清理资源
    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
      this.emailService.stopAllConnections();
    });
  }

  setupMenu() {
    // 隐藏菜单栏
    Menu.setApplicationMenu(null);
  }

  setupIPC() {
    // 获取所有邮箱账号
    ipcMain.handle('get-email-accounts', async () => {
      return await this.database.getEmailAccounts();
    });

    // 添加邮箱账号
    ipcMain.handle('add-email-account', async (event, accountData) => {
      const account = await this.database.addEmailAccount(accountData);
      if (account) {
        // 启动邮件监听
        await this.emailService.startConnection(account, (verificationCode) => {
          this.mainWindow.webContents.send('new-verification-code', {
            accountId: account.id,
            code: verificationCode
          });
        });
      }
      return account;
    });

    // 删除邮箱账号
    ipcMain.handle('delete-email-account', async (event, accountId) => {
      await this.emailService.stopConnection(accountId);
      return await this.database.deleteEmailAccount(accountId);
    });

    // 更新邮箱账号
    ipcMain.handle('update-email-account', async (event, accountId, accountData) => {
      const account = await this.database.updateEmailAccount(accountId, accountData);
      if (account) {
        // 重启邮件监听
        await this.emailService.stopConnection(accountId);
        await this.emailService.startConnection(account, (verificationCode) => {
          this.mainWindow.webContents.send('new-verification-code', {
            accountId: account.id,
            code: verificationCode
          });
        });
      }
      return account;
    });

    // 获取验证码历史
    ipcMain.handle('get-verification-codes', async (event, accountId, limit = 50) => {
      return await this.database.getVerificationCodes(accountId, limit);
    });

    // 测试邮箱连接
    ipcMain.handle('test-email-connection', async (event, accountData) => {
      return await this.emailService.testConnection(accountData);
    });

    // 启动所有邮箱监听
    ipcMain.handle('start-all-connections', async () => {
      const accounts = await this.database.getEmailAccounts();
      for (const account of accounts) {
        if (account.enabled) {
          await this.emailService.startConnection(account, (verificationCode) => {
            this.mainWindow.webContents.send('new-verification-code', {
              accountId: account.id,
              code: verificationCode
            });
          });
        }
      }
    });

    // 停止所有邮箱监听
    ipcMain.handle('stop-all-connections', async () => {
      await this.emailService.stopAllConnections();
    });

    // 获取连接状态
    ipcMain.handle('get-connection-states', async () => {
      return this.emailService.getAllConnectionStates();
    });

    // 启动单个邮箱连接
    ipcMain.handle('start-email-connection', async (event, accountId) => {
      try {
        const account = await this.database.getEmailAccount(accountId);
        if (!account) {
          throw new Error('邮箱账号不存在');
        }

        await this.emailService.startConnection(account, (verificationCode) => {
          if (this.mainWindow && !this.mainWindow.isDestroyed()) {
            this.mainWindow.webContents.send('new-verification-code', {
              accountId: account.id,
              code: verificationCode
            });
          }
        });

        return { success: true, message: '连接启动成功' };
      } catch (error) {
        console.error(`启动邮箱连接失败 [${accountId}]:`, error);
        return { success: false, message: error.message };
      }
    });

    // 停止单个邮箱连接
    ipcMain.handle('stop-email-connection', async (event, accountId) => {
      try {
        await this.emailService.stopConnection(accountId);
        return { success: true, message: '连接停止成功' };
      } catch (error) {
        console.error(`停止邮箱连接失败 [${accountId}]:`, error);
        return { success: false, message: error.message };
      }
    });

    // 手动刷新邮件
    ipcMain.handle('refresh-emails', async (event, accountId = null) => {
      try {
        if (accountId) {
          // 刷新单个账号的邮件
          await this.emailService.refreshEmails(accountId);
        } else {
          // 刷新所有账号的邮件
          await this.emailService.refreshAllEmails();
        }
        return { success: true, message: '邮件刷新成功' };
      } catch (error) {
        console.error('刷新邮件失败:', error);
        return { success: false, message: error.message };
      }
    });
  }
}

// 创建并初始化主进程
const mainProcess = new MainProcess();
mainProcess.initialize().catch(console.error);
